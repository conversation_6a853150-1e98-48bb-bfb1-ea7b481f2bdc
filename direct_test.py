#!/usr/bin/env python3
"""
直接测试认证函数
"""
import sys
import asyncio
sys.path.insert(0, '/app')

async def test_direct_auth():
    """直接测试认证函数"""
    from fastapi import Request
    from fastapi.datastructures import FormData
    from starlette.datastructures import FormData as StarletteFormData
    from app.core.security import verify_auth_token
    from app.config.config import settings
    
    print("=== 直接测试认证逻辑 ===")
    
    # 测试1: 直接调用verify_auth_token
    print("1. 直接调用verify_auth_token:")
    token = 'sk-lingod'
    result = verify_auth_token(token)
    print(f"   输入: {repr(token)}")
    print(f"   结果: {result}")
    print()
    
    # 测试2: 模拟表单数据
    print("2. 模拟表单数据处理:")
    
    # 创建模拟的表单数据
    form_data = [
        ('auth_token', 'sk-lingod')
    ]
    
    # 使用Starlette的FormData
    form = StarletteFormData(form_data)
    auth_token = form.get('auth_token')
    
    print(f"   表单数据: {form_data}")
    print(f"   获取的token: {repr(auth_token)}")
    print(f"   token类型: {type(auth_token)}")
    
    if auth_token:
        verify_result = verify_auth_token(auth_token)
        print(f"   验证结果: {verify_result}")
    else:
        print("   ❌ 无法从表单获取token!")
    print()
    
    # 测试3: 检查settings
    print("3. 检查配置:")
    print(f"   AUTH_TOKEN: {repr(settings.AUTH_TOKEN)}")
    print(f"   ALLOWED_TOKENS: {settings.ALLOWED_TOKENS}")

if __name__ == "__main__":
    asyncio.run(test_direct_auth())
