#!/usr/bin/env python3
"""
调试异常处理
"""
import sys
import asyncio
import traceback
sys.path.insert(0, '/app')

async def test_form_processing():
    """测试表单处理中的异常"""
    from fastapi import Request
    from fastapi.datastructures import FormData
    from starlette.requests import Request as StarletteRequest
    from starlette.datastructures import FormData as StarletteFormData
    from app.core.security import verify_auth_token
    from app.config.config import settings
    
    print("=== 测试表单处理异常 ===")
    
    try:
        # 模拟HTTP请求的表单数据
        import io
        from starlette.datastructures import Headers
        
        # 创建表单数据
        form_data = b'auth_token=sk-lingod'
        
        # 模拟请求
        scope = {
            'type': 'http',
            'method': 'POST',
            'path': '/auth',
            'headers': [
                (b'content-type', b'application/x-www-form-urlencoded'),
                (b'content-length', str(len(form_data)).encode()),
            ],
        }
        
        async def receive():
            return {
                'type': 'http.request',
                'body': form_data,
                'more_body': False
            }
        
        async def send(message):
            pass
        
        # 创建请求对象
        request = StarletteRequest(scope, receive, send)
        
        # 测试表单解析
        print("1. 测试表单解析:")
        form = await request.form()
        print(f"   表单对象: {form}")
        print(f"   表单类型: {type(form)}")
        
        auth_token = form.get('auth_token')
        print(f"   获取的token: {repr(auth_token)}")
        print(f"   token类型: {type(auth_token)}")
        
        if auth_token:
            print(f"   验证结果: {verify_auth_token(auth_token)}")
        else:
            print("   ❌ 无法获取token!")
            
        # 测试所有键
        print(f"   表单所有键: {list(form.keys())}")
        for key in form.keys():
            value = form.get(key)
            print(f"   {key}: {repr(value)} ({type(value)})")
            
    except Exception as e:
        print(f"❌ 异常: {e}")
        print(f"异常类型: {type(e)}")
        print("异常堆栈:")
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_form_processing())
