#!/usr/bin/env python3
"""
终极测试 - 完全模拟认证流程
"""
import sys
import asyncio
sys.path.insert(0, '/app')

async def ultimate_auth_test():
    """终极认证测试"""
    from fastapi import FastAPI, Request
    from fastapi.responses import RedirectResponse
    from starlette.requests import Request as StarletteRequest
    from app.core.security import verify_auth_token
    from app.config.config import settings
    from app.log.logger import get_main_logger
    
    logger = get_main_logger()
    
    print("=== 终极认证测试 ===")
    
    # 1. 基础验证
    print("1. 基础验证:")
    test_token = 'sk-lingod'
    basic_result = verify_auth_token(test_token)
    print(f"   verify_auth_token('{test_token}'): {basic_result}")
    print(f"   settings.AUTH_TOKEN: {repr(settings.AUTH_TOKEN)}")
    print(f"   直接比较: {test_token == settings.AUTH_TOKEN}")
    print()
    
    # 2. 完全模拟认证路由
    print("2. 模拟认证路由:")
    
    async def mock_authenticate(request: Request):
        """模拟认证函数"""
        try:
            print("   开始处理认证请求...")
            form = await request.form()
            print(f"   表单数据: {dict(form)}")
            
            auth_token = form.get("auth_token")
            print(f"   获取的auth_token: {repr(auth_token)}")
            
            if not auth_token:
                print("   ❌ 认证失败: 空token")
                return RedirectResponse(url="/", status_code=302)

            print(f"   验证中: verify_auth_token({repr(auth_token)})")
            if verify_auth_token(auth_token):
                print("   ✅ 认证成功!")
                response = RedirectResponse(url="/config", status_code=302)
                response.set_cookie(
                    key="auth_token", value=auth_token, httponly=True, max_age=settings.ADMIN_SESSION_EXPIRE
                )
                return response
            print(f"   ❌ 认证失败: 无效token")
            return RedirectResponse(url="/", status_code=302)
        except Exception as e:
            print(f"   ❌ 异常: {e}")
            import traceback
            traceback.print_exc()
            return RedirectResponse(url="/", status_code=302)
    
    # 创建模拟请求
    form_data = b'auth_token=sk-lingod'
    scope = {
        'type': 'http',
        'method': 'POST',
        'path': '/auth',
        'headers': [
            (b'content-type', b'application/x-www-form-urlencoded'),
            (b'content-length', str(len(form_data)).encode()),
        ],
    }
    
    async def receive():
        return {
            'type': 'http.request',
            'body': form_data,
            'more_body': False
        }
    
    async def send(message):
        pass
    
    request = StarletteRequest(scope, receive, send)
    
    # 执行模拟认证
    response = await mock_authenticate(request)
    print(f"   响应状态: {response.status_code}")
    print(f"   重定向位置: {response.headers.get('location')}")
    
    if response.headers.get('location') == '/config':
        print("   🎉 模拟认证成功!")
        return True
    else:
        print("   💥 模拟认证失败!")
        return False

if __name__ == "__main__":
    result = asyncio.run(ultimate_auth_test())
    print(f"\n最终结果: {'成功' if result else '失败'}")
