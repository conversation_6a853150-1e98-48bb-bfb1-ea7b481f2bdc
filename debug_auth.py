#!/usr/bin/env python3
"""
调试认证问题的脚本
"""
import requests
import sys
import os

# 添加应用路径
sys.path.insert(0, '/app')

def test_backend_auth():
    """测试后端认证逻辑"""
    from app.core.security import verify_auth_token
    from app.config.config import settings

    print("=== 后端认证测试 ===")
    token = 'sk-lingod'
    print(f"测试令牌: {repr(token)}")
    print(f"配置令牌: {repr(settings.AUTH_TOKEN)}")
    print(f"直接比较: {token == settings.AUTH_TOKEN}")
    print(f"verify_auth_token: {verify_auth_token(token)}")
    print()

def test_form_processing():
    """测试表单数据处理"""
    print("=== 表单数据处理测试 ===")

    # 模拟FastAPI的表单处理
    from fastapi.datastructures import FormData
    from starlette.datastructures import FormData as StarletteFormData

    # 创建模拟表单数据
    form_data = [
        (b'auth_token', b'sk-lingod')
    ]

    form = StarletteFormData(form_data)
    auth_token = form.get('auth_token')

    print(f"表单中的令牌: {repr(auth_token)}")
    print(f"令牌类型: {type(auth_token)}")

    # 测试认证
    from app.core.security import verify_auth_token
    result = verify_auth_token(auth_token)
    print(f"认证结果: {result}")
    print()

def test_http_auth():
    """测试HTTP认证流程"""

    # 测试数据
    test_data = {
        'auth_token': 'sk-lingod'
    }

    print("=== HTTP认证测试 ===")
    print(f"发送数据: {test_data}")

    # 发送POST请求
    response = requests.post(
        'http://localhost:8000/auth',
        data=test_data,
        allow_redirects=False
    )

    print(f"状态码: {response.status_code}")
    print(f"响应头: {dict(response.headers)}")
    print(f"重定向位置: {response.headers.get('Location', 'None')}")

    if response.headers.get('Location') == '/config':
        print("✅ 认证成功!")
        return True
    elif response.headers.get('Location') == '/':
        print("❌ 认证失败!")
        return False
    else:
        print("? 未知响应")
        return False

if __name__ == "__main__":
    test_backend_auth()
    test_form_processing()
    test_http_auth()
