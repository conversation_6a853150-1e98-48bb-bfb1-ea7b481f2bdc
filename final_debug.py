#!/usr/bin/env python3
"""
最终调试 - 直接模拟完整的认证流程
"""
import sys
import asyncio
sys.path.insert(0, '/app')

async def simulate_full_auth():
    """完整模拟认证流程"""
    from fastapi import Request
    from fastapi.responses import RedirectResponse
    from starlette.requests import Request as StarletteRequest
    from app.core.security import verify_auth_token
    from app.config.config import settings
    
    print("=== 完整认证流程模拟 ===")
    
    # 1. 检查配置
    print("1. 配置检查:")
    print(f"   AUTH_TOKEN: {repr(settings.AUTH_TOKEN)}")
    print(f"   ALLOWED_TOKENS: {settings.ALLOWED_TOKENS}")
    print()
    
    # 2. 直接验证函数测试
    print("2. 直接验证函数:")
    test_token = 'sk-lingod'
    direct_result = verify_auth_token(test_token)
    print(f"   verify_auth_token('{test_token}'): {direct_result}")
    print()
    
    # 3. 模拟完整的HTTP请求处理
    print("3. 模拟HTTP请求处理:")
    
    try:
        # 创建表单数据
        form_data = b'auth_token=sk-lingod'
        
        # 模拟请求
        scope = {
            'type': 'http',
            'method': 'POST',
            'path': '/auth',
            'headers': [
                (b'content-type', b'application/x-www-form-urlencoded'),
                (b'content-length', str(len(form_data)).encode()),
            ],
        }
        
        async def receive():
            return {
                'type': 'http.request',
                'body': form_data,
                'more_body': False
            }
        
        async def send(message):
            pass
        
        # 创建请求对象
        request = StarletteRequest(scope, receive, send)
        
        # 模拟认证路由逻辑
        print("   开始处理表单...")
        form = await request.form()
        print(f"   表单解析结果: {dict(form)}")
        
        auth_token = form.get("auth_token")
        print(f"   获取的auth_token: {repr(auth_token)}")
        
        if not auth_token:
            print("   ❌ 认证失败: 空token")
            return False
        
        if verify_auth_token(auth_token):
            print("   ✅ 认证成功!")
            return True
        else:
            print("   ❌ 认证失败: 无效token")
            return False
            
    except Exception as e:
        print(f"   ❌ 异常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    result = asyncio.run(simulate_full_auth())
    print(f"\n最终结果: {'成功' if result else '失败'}")
