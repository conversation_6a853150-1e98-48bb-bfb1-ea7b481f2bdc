#!/usr/bin/env python3
"""
字符级别调试
"""
import sys
import asyncio
sys.path.insert(0, '/app')

async def char_level_debug():
    """字符级别的调试"""
    from starlette.requests import Request as StarletteRequest
    from app.core.security import verify_auth_token
    from app.config.config import settings
    
    print("=== 字符级别调试 ===")
    
    # 1. 检查配置中的token
    config_token = settings.AUTH_TOKEN
    print(f"1. 配置token:")
    print(f"   值: {repr(config_token)}")
    print(f"   长度: {len(config_token)}")
    print(f"   字符: {[ord(c) for c in config_token]}")
    print()
    
    # 2. 模拟表单数据
    form_data = b'auth_token=sk-lingod'
    print(f"2. 表单数据:")
    print(f"   原始: {form_data}")
    print(f"   解码: {form_data.decode()}")
    print()
    
    # 3. 解析表单
    scope = {
        'type': 'http',
        'method': 'POST',
        'path': '/auth',
        'headers': [
            (b'content-type', b'application/x-www-form-urlencoded'),
            (b'content-length', str(len(form_data)).encode()),
        ],
    }
    
    async def receive():
        return {
            'type': 'http.request',
            'body': form_data,
            'more_body': False
        }
    
    async def send(message):
        pass
    
    request = StarletteRequest(scope, receive, send)
    form = await request.form()
    
    form_token = form.get('auth_token')
    print(f"3. 表单解析结果:")
    print(f"   值: {repr(form_token)}")
    print(f"   长度: {len(form_token) if form_token else 'None'}")
    if form_token:
        print(f"   字符: {[ord(c) for c in form_token]}")
    print()
    
    # 4. 逐字符比较
    print(f"4. 逐字符比较:")
    if form_token and config_token:
        print(f"   长度相等: {len(form_token) == len(config_token)}")
        print(f"   直接相等: {form_token == config_token}")
        print(f"   repr相等: {repr(form_token) == repr(config_token)}")
        
        if len(form_token) == len(config_token):
            for i, (c1, c2) in enumerate(zip(form_token, config_token)):
                if c1 != c2:
                    print(f"   差异在位置 {i}: {repr(c1)} vs {repr(c2)} ({ord(c1)} vs {ord(c2)})")
                    break
            else:
                print("   所有字符都相同")
    print()
    
    # 5. 验证测试
    print(f"5. 验证测试:")
    if form_token:
        result = verify_auth_token(form_token)
        print(f"   verify_auth_token(form_token): {result}")
    
    result2 = verify_auth_token(config_token)
    print(f"   verify_auth_token(config_token): {result2}")
    
    # 6. 手动验证逻辑
    print(f"6. 手动验证:")
    if form_token:
        manual_result = (form_token == config_token)
        print(f"   form_token == config_token: {manual_result}")

if __name__ == "__main__":
    asyncio.run(char_level_debug())
