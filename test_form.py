#!/usr/bin/env python3
"""
测试表单数据处理
"""
import requests

def test_different_formats():
    """测试不同的表单数据格式"""
    
    base_url = 'http://localhost:8000/auth'
    
    # 测试1: 标准表单数据
    print("=== 测试1: 标准表单数据 ===")
    data1 = {'auth_token': 'sk-lingod'}
    response1 = requests.post(base_url, data=data1, allow_redirects=False)
    print(f"结果: {response1.status_code} -> {response1.headers.get('Location')}")
    
    # 测试2: 显式Content-Type
    print("\n=== 测试2: 显式Content-Type ===")
    headers2 = {'Content-Type': 'application/x-www-form-urlencoded'}
    response2 = requests.post(base_url, data=data1, headers=headers2, allow_redirects=False)
    print(f"结果: {response2.status_code} -> {response2.headers.get('Location')}")
    
    # 测试3: 手动编码
    print("\n=== 测试3: 手动编码 ===")
    from urllib.parse import urlencode
    encoded_data = urlencode(data1)
    print(f"编码后数据: {encoded_data}")
    response3 = requests.post(base_url, data=encoded_data, headers=headers2, allow_redirects=False)
    print(f"结果: {response3.status_code} -> {response3.headers.get('Location')}")
    
    # 测试4: JSON数据 (应该失败)
    print("\n=== 测试4: JSON数据 ===")
    import json
    json_data = json.dumps(data1)
    headers4 = {'Content-Type': 'application/json'}
    response4 = requests.post(base_url, data=json_data, headers=headers4, allow_redirects=False)
    print(f"结果: {response4.status_code} -> {response4.headers.get('Location')}")

if __name__ == "__main__":
    test_different_formats()
