#!/usr/bin/env python3
"""
创建临时认证测试路由
"""
import sys
import asyncio
sys.path.insert(0, '/app')

from fastapi import FastAPI, Request
from fastapi.responses import JSONResponse
from app.core.security import verify_auth_token
from app.config.config import settings
import uvicorn

# 创建测试应用
test_app = FastAPI()

@test_app.post("/test-auth")
async def test_authenticate(request: Request):
    """测试认证路由"""
    try:
        print("=== 开始处理认证请求 ===")
        
        # 获取表单数据
        form = await request.form()
        print(f"表单数据: {dict(form)}")
        
        auth_token = form.get("auth_token")
        print(f"获取的token: {repr(auth_token)}")
        print(f"token类型: {type(auth_token)}")
        print(f"配置的AUTH_TOKEN: {repr(settings.AUTH_TOKEN)}")
        
        if not auth_token:
            print("❌ 没有获取到auth_token")
            return JSONResponse({"status": "error", "message": "No auth_token"})
        
        # 验证token
        is_valid = verify_auth_token(auth_token)
        print(f"验证结果: {is_valid}")
        
        if is_valid:
            print("✅ 认证成功!")
            return JSONResponse({"status": "success", "message": "Authentication successful"})
        else:
            print("❌ 认证失败!")
            return JSONResponse({"status": "error", "message": "Invalid token"})
            
    except Exception as e:
        print(f"❌ 异常: {e}")
        import traceback
        traceback.print_exc()
        return JSONResponse({"status": "error", "message": str(e)})

@test_app.get("/test-config")
async def test_config():
    """测试配置"""
    return {
        "AUTH_TOKEN": repr(settings.AUTH_TOKEN),
        "ALLOWED_TOKENS": settings.ALLOWED_TOKENS,
        "verify_test": verify_auth_token('sk-lingod')
    }

if __name__ == "__main__":
    print("启动测试服务器在端口 8001...")
    uvicorn.run(test_app, host="0.0.0.0", port=8001)
