#!/usr/bin/env python3
"""
直接修补容器内的认证路由
"""
import sys
sys.path.insert(0, '/app')

# 读取原始文件
with open('/app/app/router/routes.py', 'r', encoding='utf-8') as f:
    content = f.read()

# 找到认证函数并添加调试
new_content = content.replace(
    'auth_token = form.get("auth_token")',
    '''auth_token = form.get("auth_token")
        print(f"[DEBUG] 表单数据: {dict(form)}")
        print(f"[DEBUG] 获取的token: {repr(auth_token)}")
        print(f"[DEBUG] 配置token: {repr(settings.AUTH_TOKEN)}")
        print(f"[DEBUG] token相等: {auth_token == settings.AUTH_TOKEN}")'''
)

# 写回文件
with open('/app/app/router/routes.py', 'w', encoding='utf-8') as f:
    f.write(new_content)

print("认证路由已修补，添加了调试信息")
print("需要重启应用才能生效")
